#!/usr/bin/env bash

# Simple test for expansion

# Set up test environment
mkdir -p "$HOME/测试目录"
touch "$HOME/测试目录/测试文件.txt"

echo "Created test file: $HOME/测试目录/测试文件.txt"

# Source bash-completion if available
if [[ -f /usr/share/bash-completion/bash_completion ]]; then
    source /usr/share/bash-completion/bash_completion
fi

# Source the completion script
source scripts/bash_pinyin_completion

# Test function
test_simple() {
    local test_path="$1"
    local description="$2"
    
    echo "Testing: $description"
    echo "  Path: $test_path"
    
    # Set up completion variables
    _cur="$test_path"
    _var="COMPREPLY"
    COMPREPLY=()
    
    # Call the completion function
    _add_completion -f
    
    echo "  Results:"
    if [[ ${#COMPREPLY[@]} -eq 0 ]]; then
        echo "    (no matches)"
    else
        for result in "${COMPREPLY[@]}"; do
            echo "    $result"
        done
    fi
    echo ""
}

echo "=== Simple expansion tests ==="

# Test basic cases that should work
test_simple "~/测试目录/cs" "Tilde with path and search"
test_simple "\$HOME/测试目录/cs" "HOME variable with path and search"

# Cleanup
rm -rf "$HOME/测试目录"
echo "Test completed."
