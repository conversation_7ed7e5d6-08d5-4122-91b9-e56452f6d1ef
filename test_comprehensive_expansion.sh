#!/usr/bin/env bash

# Comprehensive test for various expansion types

# Set up test environment
export TEST_VAR="/tmp/test_expansion"
mkdir -p "$TEST_VAR"
mkdir -p "$HOME/测试目录"
touch "$TEST_VAR/测试文件.txt"
touch "$HOME/测试目录/测试文件.txt"

echo "Created test files in:"
echo "  $TEST_VAR/"
echo "  $HOME/测试目录/"

# Source bash-completion if available
if [[ -f /usr/share/bash-completion/bash_completion ]]; then
    source /usr/share/bash-completion/bash_completion
elif [[ -f /etc/bash_completion ]]; then
    source /etc/bash_completion
fi

# Source the completion script
source scripts/bash_pinyin_completion

# Test function
test_expansion() {
    local test_path="$1"
    local search_term="$2"
    local description="$3"
    
    echo "Testing: $description"
    echo "  Path: $test_path$search_term"
    
    # Set up completion variables
    _cur="$test_path$search_term"
    _var="COMPREPLY"
    COMPREPLY=()
    
    # Call the completion function
    _add_completion -f
    
    echo "  Results:"
    if [[ ${#COMPREPLY[@]} -eq 0 ]]; then
        echo "    (no matches)"
    else
        for result in "${COMPREPLY[@]}"; do
            echo "    $result"
        done
    fi
    echo ""
}

echo "=== Testing various expansion types ==="
echo ""

# Test tilde expansion
test_expansion "~/测试目录/" "cs" "Tilde expansion (~)"

# Test $HOME expansion
test_expansion "\$HOME/测试目录/" "cs" "HOME variable expansion"

# Test custom variable expansion
test_expansion "\$TEST_VAR/" "cs" "Custom variable expansion"

# Test mixed expansion (if PWD is set to a useful location)
cd "$HOME"
test_expansion "\$PWD/测试目录/" "cs" "PWD variable expansion"

# Test without expansion (baseline)
test_expansion "$HOME/测试目录/" "cs" "Direct path (no expansion needed)"

# Test edge cases
test_expansion "~" "" "Just tilde"
test_expansion "\$HOME" "" "Just \$HOME"

echo "=== Testing with different search patterns ==="
echo ""

# Test with different pinyin patterns
test_expansion "~/测试目录/" "test" "Tilde + English search"
test_expansion "\$HOME/测试目录/" "wj" "HOME + pinyin search"

# Cleanup
rm -rf "$TEST_VAR"
rm -rf "$HOME/测试目录"
echo "Cleanup completed."
