#!/usr/bin/env bash

# Test script to verify tilde and variable expansion works correctly

# Create a test directory structure in home
mkdir -p "$HOME/测试目录/子目录"
touch "$HOME/测试目录/测试文件.txt"
touch "$HOME/测试目录/子目录/另一个文件.txt"

echo "Created test files in $HOME/测试目录/"

# First source bash-completion if available
if [[ -f /usr/share/bash-completion/bash_completion ]]; then
    source /usr/share/bash-completion/bash_completion
elif [[ -f /etc/bash_completion ]]; then
    source /etc/bash_completion
else
    echo "Warning: bash-completion not found, some features may not work"
fi

# Source the completion script
source scripts/bash_pinyin_completion

# Test function to simulate completion
test_completion() {
    local test_path="$1"
    local search_term="$2"
    
    echo "Testing completion for: $test_path with search term: $search_term"
    
    # Set up the variables that the completion function expects
    _cur="$test_path$search_term"
    _var="COMPREPLY"
    COMPREPLY=()
    
    # Call the completion function
    _add_completion -f
    
    echo "Results:"
    for result in "${COMPREPLY[@]}"; do
        echo "  $result"
    done
    echo ""
}

echo "Testing tilde expansion:"
test_completion "~/测试目录/" "cs"

echo "Testing \$HOME expansion:"
test_completion "\$HOME/测试目录/" "cs"

echo "Testing \$USER expansion (if USER is set):"
if [[ -n "$USER" ]]; then
    # Create a test in /tmp for this
    mkdir -p "/tmp/test_$USER"
    touch "/tmp/test_$USER/测试文件.txt"
    test_completion "\$USER" ""
    rm -rf "/tmp/test_$USER"
fi

echo "Testing regular path (should work as before):"
test_completion "$HOME/测试目录/" "cs"

# Cleanup
rm -rf "$HOME/测试目录"
echo "Cleanup completed."
