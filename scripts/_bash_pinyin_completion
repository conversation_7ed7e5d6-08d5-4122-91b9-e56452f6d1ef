

# # Fallback
# # Backup original _comp_complete_load if it exists
# if declare -F _comp_complete_load &>/dev/null; then
#     eval "function __bak_comp_complete_load() { $(declare -f _comp_complete_load | tail -n +2) }"
    
#     _comp_complete_load() {
#         __bak_comp_complete_load "$@"
#         local result=$?
        
#         if [[ ${#COMPREPLY[@]} -gt 0 ]] && _looks_like_file_completion; then
#             _enhance_compreply_with_pinyin
#         fi
        
#         return $result
#     }
# fi

# # Helper function to detect if current completion context is file-related
# _looks_like_file_completion() {
#     local item
#     for item in "${COMPREPLY[@]:0:5}"; do
#         if [[ -e "$item" ]] || [[ "$item" == */* ]]; then
#             return 0
#         fi
#     done
#     return 1
# }

# _enhance_compreply_with_pinyin() {
#     [[ ${#COMP_WORDS[@]} -eq 0 ]] || [[ -z "${COMP_CWORD+x}" ]] && return
    
#     local cur="${COMP_WORDS[COMP_CWORD]}"
#     [[ -z "$cur" ]] && return
    
#     local dirpart basepart
#     if [[ "${cur:0:1}" == "'" || "${cur:0:1}" == "\"" ]]; then
#         dirpart="$(dirname -- "${cur:1}")"
#         basepart="$(basename -- "${cur:1}")"
#     else
#         dirpart="$(dirname -- "$cur")"
#         basepart="$(basename -- "$cur")"
#     fi
    
#     [[ "$dirpart" == "." && "${cur:0:2}" != "./" ]] && dirpart=""
    
#     local savedPWD="$PWD"
#     local resolved_dir
    
#     if [[ -n "$dirpart" ]]; then
#         resolved_dir="$(realpath -- "$dirpart" 2>/dev/null)"
#         if [[ -d "$resolved_dir" ]]; then
#             cd -- "$resolved_dir" 2>/dev/null || return
#         else
#             return
#         fi
#     fi
    
#     local -a pinyin_matched
#     mapfile -t pinyin_matched < <(
#         compgen -f -- 2>/dev/null |
#         bash-pinyin-completion-rs "$basepart" 2>/dev/null
#     )
    
#     cd "$savedPWD" || return
    
#     if [[ ${#pinyin_matched[@]} -gt 0 ]]; then
#         if [[ -n "$dirpart" ]]; then
#             local sep="/"
#             [[ "$dirpart" == "/" ]] && sep=""
            
#             for i in "${!pinyin_matched[@]}"; do
#                 pinyin_matched[$i]="${dirpart}${sep}${pinyin_matched[$i]}"
#             done
#         fi

#         # Merge and deduplicate
#         local -a old_candidates=("${COMPREPLY[@]}")
#         COMPREPLY=("${old_candidates[@]}" "${pinyin_matched[@]}")
        
#         declare -A seen
#         local -a unique_compreply=()
#         for item in "${COMPREPLY[@]}"; do
#             if [[ -z "${seen[$item]}" ]]; then
#                 seen["$item"]=1
#                 unique_compreply+=("$item")
#             fi
#         done
#         COMPREPLY=("${unique_compreply[@]}")
#     fi
# }
