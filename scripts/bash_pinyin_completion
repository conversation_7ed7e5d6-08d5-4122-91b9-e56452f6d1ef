#!/usr/bin/env bash

# Anthon Open Source Community 
# Pinyin Completion Hook for Bash-Completion

# Detect bash-completion
if ! declare -F _comp_compgen__call_builtin &>/dev/null; then
    echo "No function _comp_compgen__call_builtin found. Please install bash-completion first."
    exit 1
fi

# Backup the original function
eval "function __bak_comp_compgen__call_builtin() { $(declare -f _comp_compgen__call_builtin | tail -n +2) }"


_comp_compgen__call_builtin() {
    __bak_comp_compgen__call_builtin "$@"
    local original_result=$?
    
    # Only add pinyin completion for file/directory completions
    local is_file_completion=false
    local compgen_args=("$@")
    
    # Check for file completion indicators
    for arg in "${compgen_args[@]}"; do
        case "$arg" in
            -f|-d|-A|file|directory)
                is_file_completion=true
                break
                ;;
        esac
    done
    
    # If this looks like file completion, add pinyin matches
    if [[ "$is_file_completion" == true ]]; then
        _add_completion "$@"
    fi
    
    return $original_result
}

# Function to add completion results
_add_completion() {
    # Check if we have the necessary variables
    if [[ -z "${_cur-}" ]] || [[ -z "${_var-}" ]]; then
        return
    fi

    local cur="$_cur"
    local var_name="$_var"

    # Skip empty
    [[ -z "$cur" ]] && return

    # Handle expansion properly
    local expanded_cur="$cur"

    # First handle tilde expansion using bash-completion's _expand
    if [[ "$cur" == '~'* ]]; then
        # Set global cur for _expand to work
        cur="$expanded_cur"
        _expand
        local expand_result=$?
        expanded_cur="$cur"

        # If _expand returned 1, it means we should return early (e.g., for bare ~)
        if [[ $expand_result -eq 1 ]]; then
            return 0
        fi
    fi

    # Then handle variable expansion manually
    if [[ "$expanded_cur" == *'$'* ]]; then
        # Use eval for variable expansion, but safely
        local temp_expanded
        eval "temp_expanded=\"$expanded_cur\"" 2>/dev/null && expanded_cur="$temp_expanded"
    fi

    # Use the expanded path for further processing
    cur="$expanded_cur"

    local dirpart basepart
    if [[ "${cur:0:1}" == "'" || "${cur:0:1}" == "\"" ]]; then
        dirpart="$(dirname -- "${cur:1}")"
        basepart="$(basename -- "${cur:1}")"
    else
        dirpart="$(dirname -- "$cur")"
        basepart="$(basename -- "$cur")"
    fi
    
    [[ "$dirpart" == "." && "${cur:0:2}" != "./" ]] && dirpart=""
    
    local savedPWD="$PWD"
    local resolved_dir
    local compgen_opts=(-f)
    
    local is_dir_only=false
    for arg in "$@"; do
        if [[ "$arg" == "-d" ]]; then
            is_dir_only=true
            compgen_opts=(-d)
            break
        fi
    done
    
    if [[ -n "$dirpart" ]]; then
        resolved_dir="$(realpath -- "$dirpart" 2>/dev/null)"
        if [[ -d "$resolved_dir" ]]; then
            cd -- "$resolved_dir" 2>/dev/null || return
        else
            cd "$savedPWD" || return
            return
        fi
    fi
    
    local -a pinyin_matched
    if [[ "$is_dir_only" == true ]]; then
        mapfile -t pinyin_matched < <(
            compgen -d -- 2>/dev/null |
            bash-pinyin-completion-rs "$basepart" 2>/dev/null
        )
    else
        mapfile -t pinyin_matched < <(
            compgen -f -- 2>/dev/null |
            bash-pinyin-completion-rs "$basepart" 2>/dev/null
        )
    fi
    
    # Restore directory
    cd "$savedPWD" || return
    
    if [[ ${#pinyin_matched[@]} -gt 0 ]]; then
        if [[ -n "$dirpart" ]]; then
            local sep="/"
            [[ "$dirpart" == "/" ]] && sep=""
            
            for i in "${!pinyin_matched[@]}"; do
                pinyin_matched[$i]="${dirpart}${sep}${pinyin_matched[$i]}"
            done
        fi
        
        
        local current_results_var="current_results"
        eval "local -a $current_results_var=(\"\${$var_name[@]}\")"
        
        # Merge results and remove duplicates
        local -a all_results
        eval "all_results=(\"\${$current_results_var[@]}\" \"\${pinyin_matched[@]}\")"
        
        declare -A seen
        local -a unique_results=()
        for item in "${all_results[@]}"; do
            if [[ -z "${seen[$item]}" ]]; then
                seen["$item"]=1
                unique_results+=("$item")
            fi
        done
        
        eval "$var_name=(\"\${unique_results[@]}\")"
    fi
}
